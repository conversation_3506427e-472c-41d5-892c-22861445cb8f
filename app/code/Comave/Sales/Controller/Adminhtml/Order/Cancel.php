<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Controller\Adminhtml\Order;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\Order;
use Psr\Log\LoggerInterface;

/**
 * Admin Order Cancellation Controller
 */
class Cancel extends Action implements HttpPostActionInterface
{
    public const string ADMIN_RESOURCE = 'Magento_Sales::cancel';

    /**
     * @param Context $context
     * @param JsonFactory $resultJsonFactory
     * @param OrderRepositoryInterface $orderRepository
     * @param LoggerInterface $logger
     */
    public function __construct(
        Context $context,
        private readonly JsonFactory $resultJsonFactory,
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly LoggerInterface $logger
    ) {
        parent::__construct($context);
    }

    /**
     * Cancel order with reason
     *
     * @return \Magento\Framework\Controller\Result\Json
     */
    public function execute()
    {
        $resultJson = $this->resultJsonFactory->create();

        try {
            $orderId = (int) $this->getRequest()->getParam('order_id');
            $reason = $this->getRequest()->getParam('reason');
            $comment = $this->getRequest()->getParam('comment', '');

            if (!$orderId) {
                throw new LocalizedException(__('Order ID is required.'));
            }

            if (!in_array($reason, ['canceled_by_seller', 'canceled_by_customer'])) {
                throw new LocalizedException(__('Invalid cancellation reason.'));
            }

            $order = $this->orderRepository->get($orderId);
            if (!$order->canCancel()) {
                throw new LocalizedException(__('This order cannot be cancelled.'));
            }

            if ($this->isOrderShipped($order)) {
                throw new LocalizedException(__('Cannot cancel order that has been shipped.'));
            }

            // Use the cancellation service for consistent logic
            $this->cancellationService->cancelOrderWithReason($order, $reason, $comment);

            $resultJson->setData([
                'success' => true,
                'message' => __('Order has been cancelled successfully.')
            ]);

        } catch (LocalizedException $e) {
            $this->logger->error('Order cancellation error: ' . $e->getMessage());
            $resultJson->setData([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Order cancellation error: ' . $e->getMessage());
            $resultJson->setData([
                'success' => false,
                'message' => __('An error occurred while cancelling the order.')
            ]);
        }

        return $resultJson;
    }

    /**
     * Check if order has been shipped
     *
     * @param Order $order
     * @return bool
     */
    private function isOrderShipped(Order $order): bool
    {
        $shippedStatuses = ['shipped', 'delivered', 'pre_transit'];
        return in_array($order->getStatus(), $shippedStatuses);
    }
}
