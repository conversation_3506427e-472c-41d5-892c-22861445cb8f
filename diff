diff --git a/app/code/Comave/Sales/Service/Order/RefundService.php b/app/code/Comave/Sales/Service/Order/RefundService.php
new file mode 100644
index 000000000..0ad5ee823
--- /dev/null
+++ b/app/code/Comave/Sales/Service/Order/RefundService.php
@@ -0,0 +1,126 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ */
+declare(strict_types=1);
+
+namespace Comave\Sales\Service\Order;
+
+use Magento\Framework\Exception\LocalizedException;
+use Magento\Sales\Model\Order;
+use Magento\Sales\Model\Order\CreditmemoFactory;
+use Magento\Sales\Api\CreditmemoManagementInterface;
+use Magento\Sales\Model\Order\Invoice;
+use Psr\Log\LoggerInterface;
+
+/**
+ * Refund Service for Order Cancellations
+ */
+class RefundService
+{
+    /**
+     * @param CreditmemoFactory $creditmemoFactory
+     * @param CreditmemoManagementInterface $creditmemoManagement
+     * @param LoggerInterface $logger
+     */
+    public function __construct(
+        private readonly CreditmemoFactory $creditmemoFactory,
+        private readonly CreditmemoManagementInterface $creditmemoManagement,
+        private readonly LoggerInterface $logger
+    ) {}
+
+    /**
+     * Process automatic refund for cancelled order
+     *
+     * @param Order $order
+     * @return void
+     * @throws LocalizedException
+     */
+    public function processAutomaticRefund(Order $order): void
+    {
+        if (!$order->canCreditmemo()) {
+            return;
+        }
+
+        try {
+            $invoice = $this->getLastInvoice($order);
+            if (!$invoice) {
+                $this->logger->info('No invoice found for refund', ['order_id' => $order->getEntityId()]);
+                return;
+            }
+
+            $creditmemo = $this->creditmemoFactory->createByInvoice($invoice);
+            if (!$creditmemo) {
+                throw new LocalizedException(__('Cannot create credit memo for this order.'));
+            }
+
+            $creditmemo->addComment(
+                __('Automatic refund due to order cancellation'),
+                false,
+                true
+            );
+
+            $isOnline = $this->canRefundOnline($order);
+            
+            $this->creditmemoManagement->refund($creditmemo, $isOnline);
+
+            $this->logger->info(
+                'Automatic refund processed successfully',
+                [
+                    'order_id' => $order->getEntityId(),
+                    'creditmemo_id' => $creditmemo->getEntityId(),
+                    'online' => $isOnline
+                ]
+            );
+
+        } catch (\Exception $e) {
+            $this->logger->error(
+                'Failed to process automatic refund',
+                [
+                    'order_id' => $order->getEntityId(),
+                    'error' => $e->getMessage()
+                ]
+            );
+            throw new LocalizedException(__('Failed to process refund: %1', $e->getMessage()));
+        }
+    }
+
+    /**
+     * Get the last invoice for the order
+     *
+     * @param Order $order
+     * @return Invoice|null
+     */
+    private function getLastInvoice(Order $order): ?Invoice
+    {
+        $invoices = $order->getInvoiceCollection();
+        
+        if ($invoices->getSize() === 0) {
+            return null;
+        }
+
+        $invoices->setOrder('created_at', 'DESC');
+        return $invoices->getFirstItem();
+    }
+
+    /**
+     * Check if order can be refunded online
+     *
+     * @param Order $order
+     * @return bool
+     */
+    private function canRefundOnline(Order $order): bool
+    {
+        $payment = $order->getPayment();
+        
+        if (!$payment) {
+            return false;
+        }
+
+        $paymentMethod = $payment->getMethodInstance();
+        
+        return $paymentMethod->canRefund() && 
+               $payment->getAmountPaid() > 0 && 
+               $payment->getBaseAmountPaidOnline() > 0;
+    }
+}
diff --git a/app/code/Comave/Sales/etc/acl.xml b/app/code/Comave/Sales/etc/acl.xml
new file mode 100644
index 000000000..7f030831b
--- /dev/null
+++ b/app/code/Comave/Sales/etc/acl.xml
@@ -0,0 +1,14 @@
+<?xml version="1.0"?>
+<!--
+/**
+ * Copyright © Commercial Avenue
+ */
+-->
+<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
+        xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
+    <acl>
+        <resources>
+            <!-- We use Magento's existing cancel permission: Magento_Sales::cancel -->
+        </resources>
+    </acl>
+</config>
diff --git a/app/code/Comave/Sales/etc/adminhtml/routes.xml b/app/code/Comave/Sales/etc/adminhtml/routes.xml
new file mode 100644
index 000000000..cbfbcf549
--- /dev/null
+++ b/app/code/Comave/Sales/etc/adminhtml/routes.xml
@@ -0,0 +1,14 @@
+<?xml version="1.0"?>
+<!--
+/**
+ * Copyright © Commercial Avenue
+ */
+-->
+<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
+        xsi:noNamespaceSchemaLocation="urn:magento:framework:App/etc/routes.xsd">
+    <router id="admin">
+        <route id="comave_sales" frontName="comave_sales">
+            <module name="Comave_Sales" />
+        </route>
+    </router>
+</config>
diff --git a/app/code/Comave/Sales/etc/di.xml b/app/code/Comave/Sales/etc/di.xml
index 7245d8da0..c4ac587fc 100644
--- a/app/code/Comave/Sales/etc/di.xml
+++ b/app/code/Comave/Sales/etc/di.xml
@@ -152,4 +152,8 @@
     <type name="Magento\Ui\Model\Export\MetadataProvider">
         <plugin name="comave_sales_format_sku_ean_export" type="Comave\Sales\Plugin\FormatSkuEanExport" sortOrder="10" />
     </type>
+    <type name="Magento\Sales\Block\Adminhtml\Order\View">
+        <plugin name="addCancelButton" type="Comave\Sales\Plugin\Adminhtml\Order\View"/>
+    </type>
+
 </config>
diff --git a/app/code/Comave/Sales/view/adminhtml/layout/sales_order_view.xml b/app/code/Comave/Sales/view/adminhtml/layout/sales_order_view.xml
index 837d49e11..8679c7ef9 100644
--- a/app/code/Comave/Sales/view/adminhtml/layout/sales_order_view.xml
+++ b/app/code/Comave/Sales/view/adminhtml/layout/sales_order_view.xml
@@ -20,5 +20,10 @@
         </referenceContainer>
         <referenceBlock name="gift_options" remove="true" />
         <referenceBlock name="gift_options_link" remove="true" />
+        <referenceContainer name="content">
+            <block class="Magento\Framework\View\Element\Template"
+                   name="order_cancel_modal"
+                   template="Comave_Sales::order/cancel_modal.phtml" />
+        </referenceContainer>
     </body>
 </page>
diff --git a/app/code/Comave/Sales/view/adminhtml/requirejs-config.js b/app/code/Comave/Sales/view/adminhtml/requirejs-config.js
new file mode 100644
index 000000000..4842270b1
--- /dev/null
+++ b/app/code/Comave/Sales/view/adminhtml/requirejs-config.js
@@ -0,0 +1,10 @@
+/**
+ * Copyright © Commercial Avenue
+ */
+var config = {
+    map: {
+        '*': {
+            orderCancelModal: 'Comave_Sales/js/order-cancel-modal'
+        }
+    }
+};
diff --git a/app/code/Comave/Sales/view/adminhtml/templates/order/cancel_modal.phtml b/app/code/Comave/Sales/view/adminhtml/templates/order/cancel_modal.phtml
new file mode 100644
index 000000000..fc7ea928d
--- /dev/null
+++ b/app/code/Comave/Sales/view/adminhtml/templates/order/cancel_modal.phtml
@@ -0,0 +1,114 @@
+<?php
+/**
+ * Copyright © Commercial Avenue
+ *
+ * @var \Magento\Framework\View\Element\Template $block
+ */
+?>
+
+<!-- Cancel Order Modal -->
+<div id="cancel-order-modal" style="display: none;">
+    <div class="cancel-order-content">
+        <form id="cancel-order-form">
+            <div class="cancel-reason-options">
+                <div class="reason-option">
+                    <input type="radio"
+                           id="reason_seller"
+                           name="reason"
+                           value="canceled_by_seller"
+                           required />
+                    <label for="reason_seller">
+                        <?= $block->escapeHtml(__('Canceled by Seller')) ?>
+                    </label>
+                </div>
+                <div class="reason-option">
+                    <input type="radio"
+                           id="reason_customer"
+                           name="reason"
+                           value="canceled_by_customer"
+                           required />
+                    <label for="reason_customer">
+                        <?= $block->escapeHtml(__('Canceled by Customer')) ?>
+                    </label>
+                </div>
+            </div>
+
+            <div class="cancel-comment">
+                <textarea id="cancel_comment"
+                          name="comment"
+                          placeholder="<?= $block->escapeHtmlAttr(__('Comment (optional)')) ?>"></textarea>
+            </div>
+
+            <div class="cancel-actions">
+                <button type="button" class="action-secondary cancel-modal-btn">
+                    <?= $block->escapeHtml(__('Close')) ?>
+                </button>
+                <button type="button" class="action-primary submit-cancel-btn">
+                    <?= $block->escapeHtml(__('Cancel Order')) ?>
+                </button>
+            </div>
+        </form>
+    </div>
+</div>
+
+<script type="text/x-magento-init">
+{
+    "#cancel-order-modal": {
+        "Comave_Sales/js/order-cancel-modal": {}
+    }
+}
+</script>
+
+<style type="text/css">
+.cancel-order-modal .modal-inner-wrap {
+    max-width: 400px;
+}
+
+.cancel-order-content {
+    padding: 20px;
+}
+
+.cancel-reason-options {
+    margin-bottom: 20px;
+}
+
+.reason-option {
+    margin-bottom: 12px;
+    display: flex;
+    align-items: center;
+}
+
+.reason-option input[type="radio"] {
+    margin-right: 8px;
+    margin-top: 0;
+}
+
+.reason-option label {
+    cursor: pointer;
+    font-weight: normal;
+    margin: 0;
+}
+
+.cancel-comment {
+    margin-bottom: 20px;
+}
+
+.cancel-comment textarea {
+    width: 100%;
+    min-height: 60px;
+    padding: 8px;
+    border: 1px solid #ccc;
+    border-radius: 3px;
+    resize: vertical;
+    font-family: inherit;
+}
+
+.cancel-actions {
+    text-align: right;
+}
+
+.cancel-actions button {
+    margin-left: 10px;
+    padding: 8px 16px;
+}
+</style>
diff --git a/app/code/Comave/Sales/view/adminhtml/web/js/order-cancel-modal.js b/app/code/Comave/Sales/view/adminhtml/web/js/order-cancel-modal.js
new file mode 100644
index 000000000..4f0888ec6
--- /dev/null
+++ b/app/code/Comave/Sales/view/adminhtml/web/js/order-cancel-modal.js
@@ -0,0 +1,167 @@
+/**
+ * Copyright © Commercial Avenue
+ */
+define([
+    'jquery',
+    'Magento_Ui/js/modal/modal',
+    'Magento_Ui/js/modal/alert',
+    'mage/translate',
+    'mage/url'
+], function ($, modal, alert, $t, urlBuilder) {
+    'use strict';
+
+    $.widget('comave.orderCancelModal', {
+        options: {
+            modalSelector: '#cancel-order-modal',
+            formSelector: '#cancel-order-form',
+            submitButtonSelector: '.submit-cancel-btn',
+            cancelButtonSelector: '.cancel-modal-btn',
+            cancelUrl: null,
+            modalOptions: {
+                type: 'popup',
+                responsive: true,
+                innerScroll: true,
+                title: null,
+                modalClass: 'cancel-order-modal',
+                buttons: []
+            }
+        },
+
+        _create: function () {
+            this._initModal();
+            this._bindEvents();
+        },
+
+        _initModal: function () {
+            var modalOptions = $.extend({}, this.options.modalOptions, {
+                title: $t('Cancel Order')
+            });
+
+            this.modal = modal(modalOptions, $(this.options.modalSelector));
+        },
+
+        _bindEvents: function () {
+            var self = this;
+
+            // Submit button click
+            $(this.options.submitButtonSelector).on('click', function (e) {
+                e.preventDefault();
+                self._submitCancelOrder();
+            });
+
+            // Cancel button click
+            $(this.options.cancelButtonSelector).on('click', function (e) {
+                e.preventDefault();
+                self.closeModal();
+            });
+
+            // Form submission
+            $(this.options.formSelector).on('submit', function (e) {
+                e.preventDefault();
+                self._submitCancelOrder();
+            });
+        },
+
+        openModal: function (cancelUrl) {
+            this.options.cancelUrl = cancelUrl;
+            this._resetForm();
+            this.modal.openModal();
+        },
+
+        closeModal: function () {
+            this.modal.closeModal();
+        },
+
+        _resetForm: function () {
+            var form = $(this.options.formSelector);
+            form.find('input[type="radio"]').prop('checked', false);
+            form.find('textarea').val('');
+        },
+
+        _submitCancelOrder: function () {
+            var self = this;
+            var form = $(this.options.formSelector);
+            var reason = form.find('input[name="reason"]:checked').val();
+            var comment = form.find('textarea[name="comment"]').val();
+
+            if (!reason) {
+                alert({
+                    content: $t('Please select a cancellation reason.')
+                });
+                return;
+            }
+
+            if (!this.options.cancelUrl) {
+                alert({
+                    content: $t('Cancel URL is not set.')
+                });
+                return;
+            }
+
+            // Show loading
+            $('body').trigger('processStart');
+
+            $.ajax({
+                url: this.options.cancelUrl,
+                type: 'POST',
+                data: {
+                    reason: reason,
+                    comment: comment,
+                    form_key: window.FORM_KEY
+                },
+                dataType: 'json',
+                success: function (response) {
+                    $('body').trigger('processStop');
+                    
+                    if (response.success) {
+                        alert({
+                            content: response.message || $t('Order has been cancelled successfully.'),
+                            actions: {
+                                always: function () {
+                                    location.reload();
+                                }
+                            }
+                        });
+                    } else {
+                        alert({
+                            content: response.message || $t('An error occurred while cancelling the order.')
+                        });
+                    }
+                },
+                error: function (xhr, status, error) {
+                    $('body').trigger('processStop');
+                    console.error('Cancel order error:', error);
+                    alert({
+                        content: $t('An error occurred while cancelling the order. Please try again.')
+                    });
+                }
+            });
+
+            this.closeModal();
+        },
+
+        destroy: function () {
+            if (this.modal) {
+                this.modal.closeModal();
+            }
+            this._super();
+        }
+    });
+
+    // Global function for backward compatibility
+    window.showCancelOrderModal = function (url) {
+        var modalWidget = $('#cancel-order-modal').data('comave-orderCancelModal');
+        if (modalWidget) {
+            modalWidget.openModal(url);
+        }
+    };
+
+    window.closeCancelOrderModal = function () {
+        var modalWidget = $('#cancel-order-modal').data('comave-orderCancelModal');
+        if (modalWidget) {
+            modalWidget.closeModal();
+        }
+    };
+
+    return $.comave.orderCancelModal;
+});
